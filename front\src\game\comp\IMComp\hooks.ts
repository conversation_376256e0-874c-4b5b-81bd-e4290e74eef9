import { useState, useEffect } from 'react'
import { IMCOMP_MAX_WIDTH, IMCOMP_MAX_HEIGHT } from './constants'
import { useRef } from 'react'
import { WorldMap } from '../../../shared/types/World'
import { isoToScreen } from './coordinateUtils'
import { FRAME_DURATION, CAMERA_UI_UPDATE_INTERVAL, FOG_VIDEO_PLAYBACK_RATE } from './constants'
import { CameraRef, createKeyDownHandler, createContextMenuHandler, createWheelHandler } from './eventHandlers'
import { terrainTextureManager } from './renderUtils'


/**
 * Хук для адаптивного размера экрана с максимальными ограничениями.
 * Возвращает { width, height } для canvas.
 */
export function useAdaptiveScreenSize(maxWidth: number = IMCOMP_MAX_WIDTH, maxHeight: number = IMCOMP_MAX_HEIGHT) {
  const [screenSize, setScreenSize] = useState({
    width: Math.min(window.innerWidth, maxWidth),
    height: Math.min(window.innerHeight, maxHeight)
  })

  useEffect(() => {
    const handleResize = () => {
      setScreenSize({
        width: Math.min(window.innerWidth, maxWidth),
        height: Math.min(window.innerHeight, maxHeight)
      })
    }
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [maxWidth, maxHeight])

  return screenSize
}
/**
 * Хуки для изометрической карты
 */
/**
 * Хук для управления видео тумана войны в канвасе
 */
export const useFogOfWarVideo = () => {
  const videoRef = useRef<HTMLVideoElement | null>(null)

  useEffect(() => {
    // Создаем скрытый видео элемент
    const video = document.createElement('video')
    video.src = '/textures/worldMap/fogOfWar/fogOfVarVid.mp4'
    video.autoplay = true
    video.loop = true
    video.muted = true
    video.playsInline = true
    video.style.display = 'none' // Скрываем элемент

    // Настройки видео из констант
    video.playbackRate = FOG_VIDEO_PLAYBACK_RATE // Скорость воспроизведения
    /*
     * Примеры скоростей:
     * 0.25 - очень медленно (в 4 раза медленнее)
     * 0.5 - медленно (в 2 раза медленнее)
     * 0.75 - немного медленнее
     * 1.0 - нормальная скорость
     * 1.5 - быстрее
     * 2.0 - в 2 раза быстрее
     */

    // Добавляем в DOM и запускаем
    document.body.appendChild(video)
    video.play().catch(console.error)

    videoRef.current = video

    // Очистка при размонтировании
    return () => {
      if (video.parentNode) {
        video.parentNode.removeChild(video)
      }
    }
  }, [])

  return videoRef
}

/**
 * Хук для хранения выбранной клетки карты
 * Возвращает выбранную клетку и функцию для её обновления
 */
export function useCellTarget() {
  const [cellTarget, setCellTarget] = useState<{
    isoX: number;
    isoY: number;
    tileData: any;
  } | null>(null)
  return [cellTarget, setCellTarget] as const
}


/**
 * Хук для предзагрузки текстур местности
 */
export const useTerrainTextures = () => {
  const [texturesLoaded, setTexturesLoaded] = useState(false)

  useEffect(() => {
    const preloadTextures = async () => {
      try {
        await terrainTextureManager.preloadAllTextures()
        setTexturesLoaded(true)
      } catch (error) {
        console.error('Failed to preload terrain textures:', error)
      }
    }

    preloadTextures()
  }, [])

  return { texturesLoaded, terrainTextureManager }
}


/**
 * Хук для центрирования камеры на карте ТОЛЬКО при загрузке мира
 */
export const useCameraCenter = (
  currentWorld: WorldMap | null,
  cameraRef: React.RefObject<CameraRef>,
  tileWidth: number,
  tileHeight: number
) => {
  const worldIdRef = useRef<string | null>(null)

  useEffect(() => {
    if (!cameraRef.current || !currentWorld) return

    // Центрируем камеру ТОЛЬКО при смене мира (новый worldId)
    const currentWorldId = currentWorld.id || 'default'
    if (worldIdRef.current === currentWorldId) return // Мир не изменился - не центрируем

    worldIdRef.current = currentWorldId

    // центр карты в тайлах (по изометрии)
    const mapSize = currentWorld.settings?.worldSize || 20
    const centerTile = (mapSize - 1) / 2

    // конвертим в экранные координаты центр карты
    const centerScreen = isoToScreen(centerTile, centerTile, tileWidth, tileHeight)

    // ставим камеру так, чтобы центр карты был в середине канваса
    cameraRef.current.x = centerScreen.x
    cameraRef.current.y = centerScreen.y

    console.log('Camera centered for new world:', currentWorldId)
  }, [currentWorld, tileWidth, tileHeight]) // Убираем draw из зависимостей!
}

/**
 * Хук для циклической отрисовки - основной рендер-цикл
 */
export const useRenderLoop = (draw: () => void) => {
  useEffect(() => {
    let animationFrameId: number
    let lastRenderTime = 0
    let frameCount = 0

    const renderLoop = (time: number) => {
      frameCount++

      // Рисуем каждый кадр для плавности (можно изменить на каждый 5й кадр если нужно)
      if (time - lastRenderTime >= FRAME_DURATION) {
        draw()
        lastRenderTime = time
      }

      animationFrameId = requestAnimationFrame(renderLoop)
    }

    animationFrameId = requestAnimationFrame(renderLoop)

    return () => cancelAnimationFrame(animationFrameId)
  }, [draw]) // Только draw в зависимостях
}

/**
 * Хук для обновления UI камеры
 */
export const useCameraUI = (
  cameraRef: React.RefObject<CameraRef>,
  setCameraUI: (camera: { x: number; y: number }) => void
) => {
  useEffect(() => {
    const interval = setInterval(() => {
      if (cameraRef.current) {
        setCameraUI({
          x: Math.round(cameraRef.current.x),
          y: Math.round(cameraRef.current.y)
        })
      }
    }, CAMERA_UI_UPDATE_INTERVAL)

    return () => clearInterval(interval)
  }, [cameraRef, setCameraUI])
}

/**
 * Хук для обработки клавиш
 */
export const useKeyboardControls = (cameraRef: React.RefObject<CameraRef>) => {
  useEffect(() => {
    const handleKeyDown = createKeyDownHandler(cameraRef)
    window.addEventListener('keydown', handleKeyDown)
    return () => {
      window.removeEventListener('keydown', handleKeyDown)
    }
  }, [cameraRef])
}

/**
 * Хук для обработки колеса мыши
 */
export const useWheelControls = (
  canvasRef: React.RefObject<HTMLCanvasElement>,
  zoom: number,
  handleZoomChange: (newZoom: number) => void,
  cameraRef: React.RefObject<CameraRef>
) => {
  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const wheelHandler = createWheelHandler(zoom, handleZoomChange, cameraRef, canvasRef)
    canvas.addEventListener('wheel', wheelHandler, { passive: false })
    return () => canvas.removeEventListener('wheel', wheelHandler)
  }, [canvasRef, zoom, handleZoomChange, cameraRef])
}

/**
 * Хук для отключения контекстного меню
 */
export const useContextMenuDisable = (canvasRef: React.RefObject<HTMLCanvasElement>) => {
  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return
    
    const handler = createContextMenuHandler()
    canvas.addEventListener('contextmenu', handler)
    return () => canvas.removeEventListener('contextmenu', handler)
  }, [canvasRef])
}

/**
 * Хук для начальной отрисовки
 */
export const useInitialDraw = (draw: () => void) => {
  useEffect(() => {
    draw()
  }, [draw])
}
