/**
 * Основной движок отрисовки изометрической карты
 */

import * as React from 'react';
import { WorldMap } from '../../../shared/types/World';
import { isoToScreen } from './coordinateUtils';
import { drawTile, drawFogOfWarVideoBackground } from './renderUtils';
import { CameraRef } from './eventHandlers';
import { FOG_TILE_OPACITY } from './constants';

/**
 * Основная функция отрисовки карты
 */
export const createDrawFunction = (
  canvasRef: React.RefObject<HTMLCanvasElement>,
  cameraRef: React.RefObject<CameraRef>,
  currentWorld: WorldMap | null,
  fogVideo: React.RefObject<HTMLVideoElement | null>,
  tileWidth: number,
  tileHeight: number,
  canvasWidth: number,
  canvasHeight: number,
  cellTarget: { isoX: number; isoY: number; tileData: any } | null
) => {
  return () => {
    const canvas = canvasRef.current;
    if (!canvas || !cameraRef.current) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    ctx.imageSmoothingEnabled = false; // отключаем сглаживание

    // Анимация тумана войны отключена - используется видео фон

    // Очищаем канвас
    ctx.clearRect(0, 0, canvasWidth, canvasHeight);

    const mapSize = currentWorld?.settings?.worldSize || 20;

    // Центр экрана
    const centerX = cameraRef.current.x;
    const centerY = cameraRef.current.y;

    // Рендерим видео фон тумана войны ПЕРЕД всеми тайлами
    if (currentWorld?.worldMap && fogVideo.current) {
      drawFogOfWarVideoBackground(
        ctx,
        fogVideo.current,
        mapSize,
        tileWidth,
        tileHeight,
        canvasWidth,
        canvasHeight,
        centerX,
        centerY
      )
    }

    // Собираем видимые тайлы для оптимизации
    const visibleTileKeys = new Set<string>();

    // Отрисовываем все тайлы
    for (let isoY = 0; isoY < mapSize; isoY++) {
      for (let isoX = 0; isoX < mapSize; isoX++) {
        const { x: screenX, y: screenY } = isoToScreen(isoX, isoY, tileWidth, tileHeight);

        const worldX = screenX + canvasWidth / 2 - centerX;
        const worldY = screenY + canvasHeight / 2 - centerY;

        // Проверяем, находится ли тайл в видимой области (грубая проверка)
        if (worldX >= -tileWidth && worldX <= canvasWidth + tileWidth && worldY >= -tileHeight && worldY <= canvasHeight + tileHeight) {
          const tileKey = `${isoX},${isoY}`;
          visibleTileKeys.add(tileKey);

          // Проверяем, есть ли туман войны на этом тайле
          const tileData = currentWorld?.worldMap?.[tileKey];
          const hasFogOfWar = tileData?.fogOfWar;

          // Устанавливаем opacity для клеток с туманом войны
          if (hasFogOfWar) {
            ctx.globalAlpha = FOG_TILE_OPACITY; // Почти прозрачные, но кликабельные
          } else {
            ctx.globalAlpha = 1.0; // Обычные клетки полностью видимы
          }

          drawTile(ctx, screenX, screenY, isoX, isoY, tileWidth, tileHeight, canvasWidth, canvasHeight, centerX, centerY, currentWorld, cellTarget);

          // Сбрасываем opacity
          ctx.globalAlpha = 1.0;
        }
      }
    }

    // Очистка анимации тумана войны больше не нужна - используется видео фон

    // Рисуем центральную точку для ориентации (скрыта по умолчанию)
    ctx.fillStyle = '#ff353500';
    ctx.beginPath();
    ctx.arc(canvasWidth / 2, canvasHeight / 2, 3, 0, 2 * Math.PI);
    ctx.fill();
   
  };
};
