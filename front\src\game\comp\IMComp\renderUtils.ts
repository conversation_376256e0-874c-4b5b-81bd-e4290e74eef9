/**
 * Утилиты для отрисовки изометрической карты
 */

import { WorldMap, WorldMapCell } from '../../../shared/types/World';
import { TerrainType } from '../../../shared/enums';
import { PLAYER_WM_TEXTURES, TILE_GAP, TERRAIN_TEXTURES, FOG_VIDEO_COVERAGE_MULTIPLIER, FOG_VIDEO_BRIGHTNESS, FOG_VIDEO_CONTRAST, FOG_VIDEO_SATURATE } from './constants';
import { getTileCenterOnScreen, isTileVisible, isoToScreen } from './coordinateUtils';


/**
 * Получает цвет заливки для тайла на основе его типа местности
 */
export const getTileColor = (tileData: WorldMapCell | undefined): { fillColor: string; strokeColor: string } => {
  let fillColor = '#0000001a'; // По умолчанию
  let strokeColor = '#444';

  if (!tileData) {
    return { fillColor, strokeColor };
  }

  // Цвета для разных типов местности
  switch (tileData.terrain) {
    case TerrainType.WATER:
      fillColor = '#123652ff';
      break;
    case TerrainType.GRASS:
      fillColor = '#1f5c28ff';
      break;
    case TerrainType.DEADFOREST:
      fillColor = '#6E370F';
      break;
    case TerrainType.MOUNTAIN:
      fillColor = '#646464';
      break;
    case TerrainType.DESERT:
      fillColor = '#daca7cff';
      break;  
    case TerrainType.SWAMP:
      fillColor = '#2a422aff';
      break;
    case TerrainType.ROAD:
      fillColor = '#2c2b2bff';
      break;
    case TerrainType.CITY:
      fillColor = '#FFFFFF';
      break;
    case TerrainType.RUINS:
      fillColor = '#3f3730ff';
      break;
    case TerrainType.WASTELAND:
      fillColor = '#615039ff';
      break;
  }

  // Если тайл заблокирован, делаем его красноватым
  if (tileData.blocked) {
    fillColor = 'rgba(255, 0, 0, 0.2)';
    strokeColor = '#800';
  }

  return { fillColor, strokeColor };
};

/**
 * Менеджер текстур местности - загружает и кэширует изображения
 */
class TerrainTextureManager {
  private textureCache = new Map<string, HTMLImageElement[]>();
  private loadingPromises = new Map<string, Promise<HTMLImageElement[]>>();

  /**
   * Получает текстуры для указанного типа местности
   */
  async getTerrainTextures(terrainType: TerrainType): Promise<HTMLImageElement[]> {
    const terrainKey = terrainType.toLowerCase() as keyof typeof TERRAIN_TEXTURES;

    // Проверяем кэш
    if (this.textureCache.has(terrainKey)) {
      return this.textureCache.get(terrainKey)!;
    }

    // Проверяем, не загружается ли уже
    if (this.loadingPromises.has(terrainKey)) {
      return this.loadingPromises.get(terrainKey)!;
    }

    // Получаем пути к текстурам
    const texturePaths = TERRAIN_TEXTURES[terrainKey];
    if (!texturePaths) {
      console.warn(`No textures found for terrain type: ${terrainType}`);
      return [];
    }

    // Создаем промис загрузки
    const loadingPromise = Promise.all(
      texturePaths.map(path => {
        return new Promise<HTMLImageElement>((resolve, reject) => {
          const img = new Image();
          img.onload = () => resolve(img);
          img.onerror = () => reject(new Error(`Failed to load texture: ${path}`));
          img.src = path;
        });
      })
    );

    this.loadingPromises.set(terrainKey, loadingPromise);

    try {
      const images = await loadingPromise;
      this.textureCache.set(terrainKey, images);
      this.loadingPromises.delete(terrainKey);
      return images;
    } catch (error) {
      this.loadingPromises.delete(terrainKey);
      console.error(`Failed to load textures for ${terrainType}:`, error);
      return [];
    }
  }

  /**
   * Синхронно получает текстуры (если они уже загружены)
   */
  getLoadedTextures(terrainType: TerrainType): HTMLImageElement[] | null {
    const terrainKey = terrainType.toLowerCase() as keyof typeof TERRAIN_TEXTURES;
    return this.textureCache.get(terrainKey) || null;
  }

  /**
   * Предзагружает все текстуры местности
   */
  async preloadAllTextures(): Promise<void> {
    const loadPromises = Object.keys(TERRAIN_TEXTURES).map(terrainKey =>
      this.getTerrainTextures(terrainKey.toUpperCase() as TerrainType)
    );

    await Promise.allSettled(loadPromises);
  }
}

// Глобальный экземпляр менеджера текстур
export const terrainTextureManager = new TerrainTextureManager();

/**
 * Отрисовывает текстуру местности на ромбовидный тайл
 * @param ctx - Контекст канваса
 * @param centerX - X координата центра тайла
 * @param centerY - Y координата центра тайла
 * @param halfTileW - Половина ширины тайла
 * @param halfTileH - Половина высоты тайла
 * @param terrainType - Тип местности
 * @param textureVariation - Вариация текстуры (0-3, по умолчанию случайная)
 * @param rotation - Поворот в радианах (по умолчанию 0)
 */
export const drawTerrainTexture = (
  ctx: CanvasRenderingContext2D,
  centerX: number,
  centerY: number,
  halfTileW: number,
  halfTileH: number,
  terrainType: TerrainType,
  textureVariation?: number,
  rotation: number = 0
): void => {
  // Получаем загруженные текстуры
  const textures = terrainTextureManager.getLoadedTextures(terrainType);
  if (!textures || textures.length === 0) {
    return; // Текстуры еще не загружены или не найдены
  }

  // Выбираем вариацию текстуры
  const variation = textureVariation !== undefined
    ? Math.max(0, Math.min(textureVariation, textures.length - 1))
    : Math.floor(Math.random() * textures.length);

  const texture = textures[variation];
  if (!texture || !texture.complete) {
    return; // Текстура не загружена
  }

  // Сохраняем состояние контекста
  ctx.save();

  // Создаем ромбовидную область отсечения
  ctx.beginPath();
  ctx.moveTo(centerX, centerY - halfTileH); // Верх
  ctx.lineTo(centerX + halfTileW, centerY); // Право
  ctx.lineTo(centerX, centerY + halfTileH); // Низ
  ctx.lineTo(centerX - halfTileW, centerY); // Лево
  ctx.closePath();
  ctx.clip();

  // Применяем поворот если нужно
  if (rotation !== 0) {
    ctx.translate(centerX, centerY);
    ctx.rotate(rotation);
    ctx.translate(-centerX, -centerY);
  }

  // Вычисляем размеры для полного покрытия ромба
  const tileWidth = halfTileW * 2;
  const tileHeight = halfTileH * 2;
  const imgRatio = texture.width / texture.height;
  const tileRatio = tileWidth / tileHeight;

  let drawW = tileWidth;
  let drawH = tileHeight;

  if (imgRatio > tileRatio) {
    // Изображение шире ромба - увеличиваем высоту
    drawH = tileWidth / imgRatio;
    if (drawH < tileHeight) drawH = tileHeight;
    drawW = drawH * imgRatio;
  } else {
    // Изображение выше ромба - увеличиваем ширину
    drawW = tileHeight * imgRatio;
    if (drawW < tileWidth) drawW = tileWidth;
    drawH = drawW / imgRatio;
  }

  // Отрисовываем текстуру
  ctx.drawImage(
    texture,
    centerX - drawW / 2,
    centerY - drawH / 2,
    drawW,
    drawH
  );

  // Восстанавливаем состояние контекста
  ctx.restore();
};

/**
 * Отрисовывает ромбовидный тайл
 */
export const drawTile = (
  ctx: CanvasRenderingContext2D,
  screenX: number,
  screenY: number,
  isoX: number,
  isoY: number,
  tileWidth: number,
  tileHeight: number,
  canvasWidth: number,
  canvasHeight: number,
  cameraX: number,
  cameraY: number,
  currentWorld: WorldMap | null,
  cellTarget: { isoX: number; isoY: number; tileData: any } | null
) => {
  const { centerX, centerY } = getTileCenterOnScreen(screenX, screenY, canvasWidth, canvasHeight, cameraX, cameraY);

  // Проверяем, находится ли тайл в видимой области
  if (!isTileVisible(centerX, centerY, tileWidth, tileHeight, canvasWidth, canvasHeight)) {
    return;
  }

  const halfTileW = tileWidth / 2 - TILE_GAP;
  const halfTileH = tileHeight / 2 - TILE_GAP;

  // Получаем данные тайла из текущего мира
  const tileKey = `${isoX},${isoY}`;
  const tileData = currentWorld?.worldMap?.[tileKey];

  // Рисуем ромб с отступом
  ctx.beginPath();
  ctx.moveTo(centerX, centerY - halfTileH); // Верх
  ctx.lineTo(centerX + halfTileW, centerY); // Право
  ctx.lineTo(centerX, centerY + halfTileH); // Низ
  ctx.lineTo(centerX - halfTileW, centerY); // Лево
  ctx.closePath();

  // Отрисовываем текстуру местности если есть данные тайла
  if (tileData?.terrain) {
    drawTerrainTexture(
      ctx,
      centerX,
      centerY,
      halfTileW,
      halfTileH,
      tileData.terrain,
      tileData.imgDirection ? tileData.imgDirection - 1 : undefined, // Используем imgDirection как вариацию текстуры
      tileData.imgDirection ? (tileData.imgDirection - 1) * Math.PI / 2 : 0 // Поворот на основе imgDirection
    );
  } else {
    // Fallback: отрисовываем цветную заливку если нет данных о местности
    const { fillColor } = getTileColor(tileData);
    ctx.fillStyle = fillColor;
    ctx.fill();
  }

  // Подсветка выбранного тайла внутренним лаймовым кантиком
  if (cellTarget && cellTarget.isoX === isoX && cellTarget.isoY === isoY) {
    // Сохраняем состояние контекста
    ctx.save();

    // Создаем внутренний ромб меньшего размера для кантика
    const insetSize = 4; // Размер отступа для кантика
    const innerHalfW = halfTileW - insetSize;
    const innerHalfH = halfTileH - insetSize;

    // Создаем область отсечения для внутреннего ромба
    ctx.beginPath();
    ctx.moveTo(centerX, centerY - innerHalfH);
    ctx.lineTo(centerX + innerHalfW, centerY);
    ctx.lineTo(centerX, centerY + innerHalfH);
    ctx.lineTo(centerX - innerHalfW, centerY);
    ctx.closePath();
    ctx.clip();

    // Рисуем лаймовую тень/кантик
    ctx.shadowColor = '#00FF00';
    ctx.shadowBlur = 8;
    ctx.shadowOffsetX = 0;
    ctx.shadowOffsetY = 0;
    ctx.strokeStyle = '#00FF00';
    ctx.lineWidth = 4;
    ctx.stroke();

    // Восстанавливаем состояние
    ctx.restore();
  }

  // Определяем направление игрока на основе целевого тайла
  let playerDirection = 0; // По умолчанию
  if (cellTarget && currentWorld?.player?.position) {
    const playerX = currentWorld.player.position.x;
    const playerY = currentWorld.player.position.y;
    const targetX = cellTarget.isoX;
    const targetY = cellTarget.isoY;

    // Вычисляем направление (0-3 для PLAYER_WM_TEXTURES[0-3])
    const deltaX = targetX - playerX;
    const deltaY = targetY - playerY;

    if (Math.abs(deltaX) > Math.abs(deltaY)) {
      // Горизонтальное движение преобладает
      playerDirection = deltaX > 0 ? 2 : 0; // Право или лево
    } else {
      // Вертикальное движение преобладает
      playerDirection = deltaY > 0 ? 1 : 3; // Вниз или вверх
    }
  }

  const playerImage = PLAYER_WM_TEXTURES[playerDirection];

  // вычисляем zoom по размеру тайла
  const baseW = 86, baseH = 86;
  const zoom = tileWidth / baseW;

  if (
    currentWorld?.player?.position &&
    isoX === currentWorld.player.position.x &&
    isoY === currentWorld.player.position.y &&
    playerImage.complete
  ) {
    const imgW = baseW * zoom;
    const imgH = baseH * zoom;
    // Центрируем по X, низ картинки совпадает с низом ромба
    ctx.drawImage(playerImage, centerX - imgW / 2, centerY + halfTileH - imgH, imgW, imgH);
  }

  // Если есть туман войны, отрисовываем его

 

  // Рисуем локацию, если есть
  if (tileData?.location && !tileData.fogOfWar) {
    ctx.fillStyle = '#FFD700';
    ctx.beginPath();
    ctx.arc(centerX, centerY - 5, 3, 0, 2 * Math.PI);
    ctx.fill();
  }
};


/**
 * Рендерит большой ромб с видео тумана войны на всю карту
 */
export const drawFogOfWarVideoBackground = (
  ctx: CanvasRenderingContext2D,
  video: HTMLVideoElement | null,
  worldSize: number,
  tileWidth: number,
  tileHeight: number,
  canvasWidth: number,
  canvasHeight: number,
  cameraX: number,
  cameraY: number
) => {
  if (!video || video.readyState < 2) return // Проверяем готовность видео

  // Правильный расчет для изометрической карты
  // Центр карты в изометрических координатах
  const mapCenterIsoX = (worldSize - 1) / 2
  const mapCenterIsoY = (worldSize - 1) / 2

  // Преобразуем центр карты в экранные координаты
  const { x: mapCenterScreenX, y: mapCenterScreenY } = isoToScreen(mapCenterIsoX, mapCenterIsoY, tileWidth, tileHeight)

  // Размер ромба карты - диагонали изометрической сетки
  const mapRomboWidth = worldSize * tileWidth * FOG_VIDEO_COVERAGE_MULTIPLIER
  const mapRomboHeight = worldSize * tileHeight * FOG_VIDEO_COVERAGE_MULTIPLIER

  // Позиция центра карты на экране с учетом камеры
  const worldCenterX = mapCenterScreenX + canvasWidth / 2 - cameraX
  const worldCenterY = mapCenterScreenY + canvasHeight / 2 - cameraY

  ctx.save()

  // Создаем ромбовидную маску точно как у изометрических тайлов
  ctx.beginPath()
  ctx.moveTo(worldCenterX, worldCenterY - mapRomboHeight / 2) // Верх
  ctx.lineTo(worldCenterX + mapRomboWidth / 2, worldCenterY) // Право
  ctx.lineTo(worldCenterX, worldCenterY + mapRomboHeight / 2) // Низ
  ctx.lineTo(worldCenterX - mapRomboWidth / 2, worldCenterY) // Лево
  ctx.closePath()
  ctx.clip()

  // Настройки фильтров из констант
  ctx.filter = `brightness(${FOG_VIDEO_BRIGHTNESS}) contrast(${FOG_VIDEO_CONTRAST}) saturate(${FOG_VIDEO_SATURATE})`

  // Рендерим видео как текстуру ромба
  ctx.drawImage(
    video,
    worldCenterX - mapRomboWidth / 2,
    worldCenterY - mapRomboHeight / 2,
    mapRomboWidth,
    mapRomboHeight
  )

  ctx.restore()
}
