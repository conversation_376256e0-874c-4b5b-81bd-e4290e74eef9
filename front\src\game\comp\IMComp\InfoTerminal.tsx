/**
 * Информационный терминал в стиле Fallout 2
 */

import * as React from 'react'
import { useState, useEffect, useRef } from 'react'
import { TerrainType } from '../../../shared/enums'
import { WorldMapCell } from '../../../shared/types/World'
import styles from './InfoTerminal.module.css'
interface TerminalMessage {
  id: number
  timestamp: string
  text: string
}

interface InfoTerminalProps {
  cellTarget: { isoX: number; isoY: number; tileData: WorldMapCell } | null
}

/**
 * Получает описание местности для терминала
 */
const getTerrainTerminalDescription = (terrain: TerrainType): string => {
  switch (terrain) {
    case TerrainType.GRASS:
      return 'луга'
    case TerrainType.DEADFOREST:
      return 'мертвый лес'
    case TerrainType.MOUNTAIN:
      return 'горная местность'
    case TerrainType.WATER:
      return 'водоем'
    case TerrainType.DESERT:
      return 'пустыня'
    case TerrainType.SWAMP:
      return 'болотистая местность'
    case TerrainType.WASTELAND:
      return 'пустошь'
    case TerrainType.ROAD:
      return 'дорога'
    case TerrainType.CITY:
      return 'городские руины'
    case TerrainType.RUINS:
      return 'руины'
    default:
      return 'неизвестная местность'
  }
}

/**
 * Форматирует время для терминала
 */
const formatTime = (): string => {
  const now = new Date()
  return `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
}

/**
 * Компонент информационного терминала
 */
export const InfoTerminal: React.FC<InfoTerminalProps> = ({ cellTarget }) => {
  const [messages, setMessages] = useState<TerminalMessage[]>([
    {
      id: 0,
      timestamp: formatTime(),
      text: '> СИСТЕМА РАЗВЕДКИ АКТИВИРОВАНА'
    },
    {
      id: 1,
      timestamp: formatTime(),
      text: '> ГОТОВ К СКАНИРОВАНИЮ МЕСТНОСТИ'
    }
  ])

  const messagesEndRef = useRef<HTMLDivElement>(null)
  const messageIdRef = useRef(2)

  // Автопрокрутка к последнему сообщению
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  // Добавляем сообщение при выборе нового тайла
  useEffect(() => {
    if (cellTarget && cellTarget.tileData) {
      const { isoX, isoY, tileData } = cellTarget;
      const terrainName = getTerrainTerminalDescription(tileData.terrain);

const fog_resposne = [
  '... хмм, ничего не видно, даже если сильно захотеть',
  '... пустота, как в голове у местного торговца',
  '... туман, как после вчерашнего',
  '... если здесь что-то и есть, то оно хорошо прячется',
  '... видимость нулевая, как у надежды на светлое будущее',
  '... только тьма и неизвестность',
  '... кажется, тут даже радиация не решилась остаться',
  '... если бы у вас был Пип-бой, он бы показал: "ничего не найдено"',
  '... вы смотрите, а в ответ — тишина',
  '... Xмм, ничего вы не видите, даже прищурившись',
];

      let message = `::\n`;
      if (tileData.fogOfWar) {
        message += `\nВы видите${fog_resposne[Math.floor(Math.random() * fog_resposne.length)]}`;
      } else {
        message += `Вы видите ${terrainName}`;
      }
      if (tileData.blocked) {
        message += `\nПохоже тут не пройти. Проход заблокирован`;
      }
      if (tileData.location) {
        message += `\nобъект: ${tileData.location.name ? tileData.location.name : 'неизвестно'}`;
      }



      const newMessage: TerminalMessage = {
        id: messageIdRef.current++,
        timestamp: formatTime(),
        text: message
      };

      setMessages(prev => [...prev, newMessage]);
    }
  }, [cellTarget])

  return (
    <div className={styles.terminal_img}>
      <div className={styles.terminal}>
        <div className={styles.terminalOverlay} />
        <div className={styles.terminalContent}>
          <div className={styles.messagesContainer}>
            {messages.map((message) => (
              <div key={message.id} className={styles.message}>
                {/* <span className={styles.timestamp}>[{message.timestamp}]</span> */}
                <span className={styles.messageText}>{message.text}</span>
              </div>
            ))}
            <div ref={messagesEndRef} />
          </div>
        </div>
      </div>
    </div>
  )
}
