.mapContainer {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
  overflow: hidden;
}

.fogOfWarVideo {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 2;
  opacity: 0.9;
  pointer-events: none;
}

.mapCanvas {
  cursor: default;
  background: transparent; /* Прозрачный фон, чтобы видео было видно */
  box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.5);
  user-select: none;
  -webkit-user-drag: none;
  position: relative;
  z-index: 3; /* Поверх видео тумана войны */
}

/* Курсор grab только при перетаскивании правой кнопкой */
.mapCanvas[data-dragging="true"] {
  cursor: grab;
}

.mapCanvas:active {
  cursor: grabbing !important;
}

.mapControls {
  position: absolute;
  top: 10px;
  left: 10px;
  background: var(--bg-overlay);
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid var(--border-color);
  backdrop-filter: blur(5px);
}


.instructions {
  color: var(--text-muted);
  font-size: 11px;
  max-width: 200px;
}

.zoomControls {
  position: absolute;
  top: 96px;
  left: 10px;
  background: var(--bg-overlay);
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid var(--border-color);
  backdrop-filter: blur(5px);
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 150px;
}

.zoomLabel {
  color: var(--text-primary);
  font-size: 12px;
  font-family: monospace;
}

.zoomSlider {
  width: 100%;
  height: 4px;
  border-radius: 2px;
  background: var(--border-color);
  outline: none;
  -webkit-appearance: none;
  appearance: none;
}

.zoomSlider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  box-shadow: 0 0 8px var(--shadow-glow);
  transition: all 0.3s ease;
}

.zoomSlider::-webkit-slider-thumb:hover {
  background: var(--primary-dark);
  transform: scale(1.1);
}

.zoomSlider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  border: none;
  box-shadow: 0 0 8px var(--shadow-glow);
  transition: all 0.3s ease;
}

.zoomSlider::-moz-range-thumb:hover {
  background: var(--primary-dark);
  transform: scale(1.1);
}
