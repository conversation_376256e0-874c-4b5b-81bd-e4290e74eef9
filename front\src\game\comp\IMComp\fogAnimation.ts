// Заглушка для совместимости - анимация тумана войны заменена на видео фон

export interface FogTileAnimationState {
  // Пустой интерфейс для совместимости
}

export class FogAnimationManager {
  update(currentTime: number): void {
    // Ничего не делаем - анимация отключена
  }

  initializeTile(tileKey: string, fogVariation: number = 1): void {
    // Ничего не делаем - анимация отключена
  }

  getTileAnimationState(tileKey: string): FogTileAnimationState | null {
    return null
  }

  getInterpolatedOpacity(state: FogTileAnimationState, imageIndex: number): number {
    return 0 // Всегда возвращаем 0 - анимация отключена
  }

  cleanup(visibleTileKeys: Set<string>): void {
    // Ничего не делаем - анимация отключена
  }

  getPerformanceStats(): { activeTiles: number; cacheSize: number } {
    return { activeTiles: 0, cacheSize: 0 }
  }
}

// Глобальный экземпляр менеджера анимации (заглушка)
export const fogAnimationManager = new FogAnimationManager()


