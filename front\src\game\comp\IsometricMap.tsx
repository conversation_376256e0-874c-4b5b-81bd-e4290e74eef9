
import * as React from 'react'
import { useRef, useState, useCallback } from 'react'
import styles from './IsometricMap.module.css'
import { WorldMap } from '../../shared/types/World'
import {
  BASE_TILE_WIDTH,
  BASE_TILE_HEIGHT,
  getScaledTileSize,
  useTerrainTextures,
  useCameraCenter,
  useRenderLoop,
  useCameraUI,
  useKeyboardControls,
  useWheelControls,
  useContextMenuDisable,
  useInitialDraw,
  createDrawFunction,
  createMouseMoveHandler,
  createZoomChangeHandler,
  createClickHandler,
  MapControls,
  ZoomControls,
  InfoTerminal,
  CameraRef,
  useAdaptiveScreenSize,
  useCellTarget,
  getCameraDisplayCoordinates,
  FOG_OF_WAR_VIDEO
} from './IMComp'
import { IMCOMP_MAX_WIDTH, IMCOMP_MAX_HEIGHT } from './IMComp/constants'
import { InfoPanel } from './IMComp/InfoPanel'

interface IsometricMapProps {
  width?: number
  height?: number
  currentWorld: WorldMap | null
}

const IsometricMap: React.FC<IsometricMapProps> = ({
  currentWorld
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const videoRef = useRef<HTMLVideoElement>(null)
  const cameraRef = useRef<CameraRef>({ x: 0, y: 0 })
  const [zoom, setZoom] = useState(1.0)
  const [cameraUI, setCameraUI] = useState({ x: 0, y: 0 })
  const { width, height } = useAdaptiveScreenSize(IMCOMP_MAX_WIDTH, IMCOMP_MAX_HEIGHT)
const [cellTarget, setCellTarget] = useCellTarget()

  // Вычисляем масштабированные размеры тайлов
  const { tileWidth, tileHeight } = getScaledTileSize(BASE_TILE_WIDTH, BASE_TILE_HEIGHT, zoom)

  // Предзагружаем текстуры местности
  useTerrainTextures()

  // Создаем функцию отрисовки - стабильная, без лишних зависимостей
  const draw = useCallback(() => {
    const drawFn = createDrawFunction(
      canvasRef,
      cameraRef,
      currentWorld,
      tileWidth,
      tileHeight,
      width,
      height,
      cellTarget
    );
    drawFn();
  }, [currentWorld, tileWidth, tileHeight, width, height, cellTarget])

  // Хук для выбранной клетки
  
  // Создаем обработчики событий
  const handleMouseMove = createMouseMoveHandler(cameraRef)
  const handleZoomChange = createZoomChangeHandler(setZoom)
  const handleClick = createClickHandler(
    canvasRef,
    cameraRef,
    currentWorld,
    tileWidth,
    tileHeight,
    width,
    height,
    setCellTarget,
    cellTarget
  )

  // Настройки видео тумана войны
  React.useEffect(() => {
    if (videoRef.current) {
      const video = videoRef.current

      // Настройки скорости воспроизведения (можно менять)
      video.playbackRate = 0.5 // 0.5 = в 2 раза медленнее, 1.0 = нормально, 2.0 = в 2 раза быстрее
      /*
       * Примеры скоростей:
       * 0.25 - очень медленно (в 4 раза медленнее)
       * 0.5 - медленно (в 2 раза медленнее)
       * 0.75 - немного медленнее
       * 1.0 - нормальная скорость
       * 1.5 - быстрее
       * 2.0 - в 2 раза быстрее
       */

      // Настройки яркости через CSS (альтернатива CSS фильтрам)
      // video.style.filter = 'brightness(0.8) contrast(1.1) saturate(0.9)'
    }
  }, [])

  // Синхронизируем позицию видео с камерой
  React.useEffect(() => {
    if (videoRef.current && cameraRef.current) {
      const video = videoRef.current
      const camera = cameraRef.current

      // Привязываем видео к позиции камеры
      // Масштабируем движение видео относительно движения камеры
      const videoScale = 0.5 // Коэффициент масштабирования движения видео
      video.style.transform = `translate(${camera.x * videoScale}px, ${camera.y * videoScale}px) scale(${zoom})`
    }
  }, [cameraUI.x, cameraUI.y, zoom]) // Обновляем при изменении камеры или зума

  // Подключаем хуки
  useCameraCenter(currentWorld, cameraRef, tileWidth, tileHeight)
  useRenderLoop(draw)
  useCameraUI(cameraRef, setCameraUI)
  useKeyboardControls(cameraRef)
  useWheelControls(canvasRef, zoom, handleZoomChange, cameraRef)
  useContextMenuDisable(canvasRef)
  useInitialDraw(draw)

  return (
    <div className={styles.mapContainer}>
      {/* Видео фон с туманом войны */}
      <video
        ref={videoRef}
        className={styles.fogOfWarVideo}
        src={FOG_OF_WAR_VIDEO}
        autoPlay
        loop
        muted
        playsInline
      />
      <canvas
        ref={canvasRef}
        width={width}
        height={height}
        className={styles.mapCanvas}
        onMouseMove={handleMouseMove}
        onClick={handleClick}
        style={{ cursor: 'default' }}
      />
      <MapControls
        displayCoordinates={getCameraDisplayCoordinates(
          cameraUI.x,
          cameraUI.y,
          currentWorld?.settings?.worldSize || 20,
          tileWidth,
          tileHeight
        )}
      />
      <ZoomControls zoom={zoom} onZoomChange={handleZoomChange} />
        <InfoPanel />
      {/* Информационный терминал в стиле Fallout */}
      <InfoTerminal cellTarget={cellTarget} />
    </div>
  )
}

export default IsometricMap
