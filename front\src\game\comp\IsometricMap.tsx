
import * as React from 'react'
import { useRef, useState, useCallback } from 'react'
import styles from './IsometricMap.module.css'
import { WorldMap } from '../../shared/types/World'
import {
  BASE_TILE_WIDTH,
  BASE_TILE_HEIGHT,
  getScaledTileSize,
  useTerrainTextures,
  useCameraCenter,
  useRenderLoop,
  useCameraUI,
  useKeyboardControls,
  useWheelControls,
  useContextMenuDisable,
  useInitialDraw,
  createDrawFunction,
  createMouseMoveHandler,
  createZoomChangeHandler,
  createClickHandler,
  MapControls,
  ZoomControls,
  InfoTerminal,
  CameraRef,
  useAdaptiveScreenSize,
  useCellTarget,
  getCameraDisplayCoordinates,
  FOG_OF_WAR_VIDEO
} from './IMComp'
import { IMCOMP_MAX_WIDTH, IMCOMP_MAX_HEIGHT } from './IMComp/constants'
import { InfoPanel } from './IMComp/InfoPanel'

interface IsometricMapProps {
  width?: number
  height?: number
  currentWorld: WorldMap | null
}

const IsometricMap: React.FC<IsometricMapProps> = ({
  currentWorld
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const cameraRef = useRef<CameraRef>({ x: 0, y: 0 })
  const [zoom, setZoom] = useState(1.0)
  const [cameraUI, setCameraUI] = useState({ x: 0, y: 0 })
  const { width, height } = useAdaptiveScreenSize(IMCOMP_MAX_WIDTH, IMCOMP_MAX_HEIGHT)
const [cellTarget, setCellTarget] = useCellTarget()

  // Вычисляем масштабированные размеры тайлов
  const { tileWidth, tileHeight } = getScaledTileSize(BASE_TILE_WIDTH, BASE_TILE_HEIGHT, zoom)

  // Предзагружаем текстуры местности
  useTerrainTextures()

  // Создаем функцию отрисовки - стабильная, без лишних зависимостей
  const draw = useCallback(() => {
    const drawFn = createDrawFunction(
      canvasRef,
      cameraRef,
      currentWorld,
      tileWidth,
      tileHeight,
      width,
      height,
      cellTarget
    );
    drawFn();
  }, [currentWorld, tileWidth, tileHeight, width, height, cellTarget])

  // Хук для выбранной клетки
  
  // Создаем обработчики событий
  const handleMouseMove = createMouseMoveHandler(cameraRef)
  const handleZoomChange = createZoomChangeHandler(setZoom)
  const handleClick = createClickHandler(
    canvasRef,
    cameraRef,
    currentWorld,
    tileWidth,
    tileHeight,
    width,
    height,
    setCellTarget,
    cellTarget
  )

  // Подключаем хуки
  useCameraCenter(currentWorld, cameraRef, tileWidth, tileHeight)
  useRenderLoop(draw)
  useCameraUI(cameraRef, setCameraUI)
  useKeyboardControls(cameraRef)
  useWheelControls(canvasRef, zoom, handleZoomChange, cameraRef)
  useContextMenuDisable(canvasRef)
  useInitialDraw(draw)

  return (
    <div className={styles.mapContainer}>
      {/* Видео фон с туманом войны */}
      <video
        className={styles.fogOfWarVideo}
        src={FOG_OF_WAR_VIDEO}
        autoPlay
        loop
        muted
        playsInline
      />
      <canvas
        ref={canvasRef}
        width={width}
        height={height}
        className={styles.mapCanvas}
        onMouseMove={handleMouseMove}
        onClick={handleClick}
        style={{ cursor: 'default' }}
      />
      <MapControls
        displayCoordinates={getCameraDisplayCoordinates(
          cameraUI.x,
          cameraUI.y,
          currentWorld?.settings?.worldSize || 20,
          tileWidth,
          tileHeight
        )}
      />
      <ZoomControls zoom={zoom} onZoomChange={handleZoomChange} />
        <InfoPanel />
      {/* Информационный терминал в стиле Fallout */}
      <InfoTerminal cellTarget={cellTarget} />
    </div>
  )
}

export default IsometricMap
