/* Информационный терминал в стиле Fallout 2 */

.terminal_img {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 450px;
  height: 360px;
  background-image: url('../../../../public/textures/gameMenu/infoTerminal.png');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  border-radius: 8px;
  
  z-index: 100;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  
}

.terminal {
  position: absolute;
  bottom: 45px;
  left: 40px;
  width: 330px;
  height: 250px;
  box-shadow: 0 0 32px 18px rgba(0,0,0,0.95);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  transform: rotate(4.5deg);
  
  color: #00ff40;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  
}


.terminalOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  border-radius: 8px;
  background: radial-gradient(circle, rgba(0,0,0,0) 50%, rgba(0,0,0,0.95) 100%);
  z-index: 2;
}


.terminalContent {
  flex: 1;
  padding: 8px;
  overflow: hidden;
  position: relative;
}

.messagesContainer {
  height: 100%;
  overflow-y: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE/Edge */
}

.messagesContainer::-webkit-scrollbar {
  display: none;
  width: 0;
  background: transparent;
}


.messagesContainer::-webkit-scrollbar {
  display: none;
  width:0;
}

.messagesContainer::-webkit-scrollbar-track {
   display: none;
  background: rgba(0, 0, 0, 0.3);
}

.messagesContainer::-webkit-scrollbar-thumb {
   display: none;
  background: #00a82a;
  border-radius: 3px;
}

.messagesContainer::-webkit-scrollbar-thumb:hover {
   display: none;
  background: #00cc33;
}

.message {
  margin-bottom: 4px;
  font-size: 11px;
  line-height: 1.3;
  display: flex;
  flex-wrap: wrap;
  animation: typeIn 0.5s ease-out;
}

.timestamp {
  color: #00aa33;
  margin-right: 6px;
  font-weight: bold;
  flex-shrink: 0;
}

.messageText {
  color: #00a82a;
  text-shadow: 0 0 3px #00a82a;
  word-break: break-word;
  flex: 1;
}




/* Адаптивность для маленьких экранов */
@media (max-width: 768px) {
  .terminal {
    width: 300px;
    height: 250px;
    bottom: 10px;
    left: 10px;
  }
  
  .message {
    font-size: 10px;
  }
  
  .terminalHeader {
    font-size: 10px;
    padding: 6px 8px;
  }
  
  .terminalFooter {
    font-size: 9px;
    padding: 4px 8px;
  }
}

@media (max-width: 480px) {
  .terminal {
    width: 250px;
    height: 200px;
  }
}
